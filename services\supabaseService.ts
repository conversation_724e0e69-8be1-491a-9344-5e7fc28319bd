import { createClient, Session, User, PostgrestSingleResponse, AuthError, Subscription } from '@supabase/supabase-js';
import { WordEntry, AIInsights, PronunciationEvaluation } from '../types';

// Supabase credentials from environment variables
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validação das variáveis de ambiente
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error(
    'Variáveis de ambiente do Supabase não configuradas. ' +
    'Verifique se VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY estão definidas no arquivo .env.local'
  );
}

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

export interface AuthSubscriptionResult {
  unsubscribe: () => void;
  isFunctional: boolean;
  subscription?: Subscription | null;
}


export const onAuthStateChange = (callback: (event: string, session: Session | null) => void): AuthSubscriptionResult => {
  const authClient = supabase.auth;
  if (authClient && typeof authClient.onAuthStateChange === 'function') {
    const { data: { subscription } } = authClient.onAuthStateChange(callback);
    if (subscription) {
      return {
        unsubscribe: () => subscription.unsubscribe(),
        isFunctional: true,
        subscription: subscription
      };
    } else {
        console.warn('supabase.auth.onAuthStateChange was called but did not return a subscription object.');
        return {
            unsubscribe: () => {
              console.warn('Attempted to unsubscribe from a listener that failed to provide a subscription object.');
            },
            isFunctional: false,
            subscription: null,
        };
    }
  } else {
    console.error(
      'CRITICAL: supabase.auth.onAuthStateChange is not a function. Real-time auth state changes will not be detected.'
    );
    // console.log('Supabase auth object at time of error:', authClient); // Already present
    // if (authClient) {
    //   console.log('Available keys on supabase.auth:', Object.keys(authClient)); // Already present
    // }
    // console.log('typeof supabase.auth.onAuthStateChanged:', typeof authClient?.onAuthStateChanged); // Already present

    return {
      unsubscribe: () => {
        console.warn('Attempted to unsubscribe from a missing or non-functional auth state listener.');
      },
      isFunctional: false,
      subscription: null,
    };
  }
};

export const signUpUser = async (email_param: string, password_param: string): Promise<{ user: User | null; error: AuthError | null }> => {
  const { data, error } = await supabase.auth.signUp({ email:email_param, password:password_param });
  return { user: data.user, error };
};

export const signInUser = async (email_param: string, password_param: string): Promise<{ session: Session | null; error: AuthError | null }> => {
  const { data, error } = await supabase.auth.signInWithPassword({ email:email_param, password:password_param });
  return { session: data.session, error };
};

export const signOutUser = async (): Promise<{ error: AuthError | null }> => {
  return supabase.auth.signOut();
};


interface SupabaseWord {
  id: string;
  user_id: string;
  text: string;
  added_date: string;
  ai_insights: AIInsights | null;
  practiced_text: string | null;
  transcription: string | null;
  pronunciation_evaluation: PronunciationEvaluation | null;
  created_at: string;
}

const mapSupabaseWordToWordEntry = (dbWord: SupabaseWord): WordEntry => ({
  id: dbWord.id,
  user_id: dbWord.user_id,
  text: dbWord.text,
  added_date: dbWord.added_date || dbWord.created_at,
  ai_insights: dbWord.ai_insights,
  practiced_text: dbWord.practiced_text,
  transcription: dbWord.transcription,
  pronunciation_evaluation: dbWord.pronunciation_evaluation,
});


export const fetchWordsForUser = async (): Promise<WordEntry[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return [];

  const { data, error }: PostgrestSingleResponse<SupabaseWord[]> = await supabase
    .from('user_words')
    .select('*')
    .eq('user_id', user.id)
    .order('added_date', { ascending: false });

  if (error) {
    console.error('Error fetching words:', error);
    throw error;
  }
  return data ? data.map(mapSupabaseWordToWordEntry) : [];
};

export const addWordToSupabase = async (wordData: Omit<WordEntry, 'id' | 'user_id' | 'added_date' | 'ai_insights' | 'practiced_text' | 'transcription' | 'pronunciation_evaluation'>): Promise<WordEntry | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not authenticated to add word.');
    return null;
  }

  const newRecord = {
    text: wordData.text,
    user_id: user.id,
    added_date: new Date().toISOString(),
    ai_insights: null,
    practiced_text: null,
    transcription: null,
    pronunciation_evaluation: null,
  };

  const { data, error }: PostgrestSingleResponse<SupabaseWord> = await supabase
    .from('user_words')
    .insert(newRecord)
    .select()
    .single();

  if (error) {
    console.error('Error adding word:', error);
    return null;
  }
  return data ? mapSupabaseWordToWordEntry(data) : null;
};

export const updateWordInSupabase = async (wordId: string, updates: Partial<Omit<WordEntry, 'id' | 'user_id' | 'added_date'>>): Promise<WordEntry | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not authenticated to update word.');
    return null;
  }

  const updateData = { ...updates };
  if ('ai_insights' in updates) updateData.ai_insights = updates.ai_insights || null;
  if ('practiced_text' in updates) updateData.practiced_text = updates.practiced_text || null;
  if ('transcription' in updates) updateData.transcription = updates.transcription || null;
  if ('pronunciation_evaluation' in updates) updateData.pronunciation_evaluation = updates.pronunciation_evaluation || null;


  const { data, error }: PostgrestSingleResponse<SupabaseWord> = await supabase
    .from('user_words')
    .update(updateData)
    .eq('id', wordId)
    .eq('user_id', user.id)
    .select()
    .single();

  if (error) {
    console.error('Error updating word:', error);
    return null;
  }
  return data ? mapSupabaseWordToWordEntry(data) : null;
};

export const deleteWordFromSupabase = async (wordId: string): Promise<boolean> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not authenticated to delete word.');
    return false;
  }

  const { error } = await supabase
    .from('user_words')
    .delete()
    .eq('id', wordId)
    .eq('user_id', user.id);

  if (error) {
    console.error('Error deleting word:', error);
    return false;
  }
  return true;
};
