# Segurança - LingoLearn

Este documento descreve as práticas de segurança implementadas no LingoLearn e como configurar o projeto de forma segura.

## 🔐 Variáveis de Ambiente

### Configuração Segura
As credenciais sensíveis agora são armazenadas em variáveis de ambiente, não mais no código fonte:

```env
# .env.local (nunca commitado no git)
GEMINI_API_KEY=sua_chave_da_api_gemini
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase
```

### Por que usar variáveis de ambiente?
- ✅ **Segurança**: Credenciais não ficam expostas no código
- ✅ **Flexibilidade**: Diferentes ambientes (dev, prod) podem usar credenciais diferentes
- ✅ **Controle de acesso**: Apenas pessoas autorizadas têm acesso às credenciais
- ✅ **Auditoria**: Mudanças de credenciais são rastreáveis

## 🛡️ Práticas de Segurança Implementadas

### 1. Separação de Credenciais
- **Antes**: Credenciais hardcoded no `supabaseService.ts`
- **Depois**: Credenciais em variáveis de ambiente com validação

### 2. Validação de Configuração
```typescript
// Validação automática das variáveis de ambiente
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Variáveis de ambiente do Supabase não configuradas');
}
```

### 3. Arquivo .gitignore
O arquivo `.env.local` é automaticamente ignorado pelo git:
```gitignore
*.local  # Inclui .env.local
```

### 4. Arquivo de Exemplo
O `.env.example` fornece um template sem credenciais reais:
```env
GEMINI_API_KEY=sua_chave_da_api_gemini_aqui
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
```

## 🔧 Como Configurar Seguramente

### 1. Desenvolvimento Local
```bash
# 1. Copie o arquivo de exemplo
cp .env.example .env.local

# 2. Edite com suas credenciais reais
nano .env.local

# 3. Nunca commite o .env.local
git status  # Deve mostrar que .env.local é ignorado
```

### 2. Produção (Vercel/Netlify)
Configure as variáveis de ambiente no painel do provedor:

**Vercel:**
1. Vá para Project Settings → Environment Variables
2. Adicione cada variável individualmente
3. Marque para todos os ambientes (Production, Preview, Development)

**Netlify:**
1. Vá para Site Settings → Environment Variables
2. Adicione cada variável
3. Deploy novamente

### 3. Outros Provedores
Consulte a documentação do seu provedor sobre como configurar variáveis de ambiente.

## 🚨 Checklist de Segurança

### Antes de Fazer Deploy
- [ ] Arquivo `.env.local` não está commitado no git
- [ ] Variáveis de ambiente configuradas no provedor de hosting
- [ ] Credenciais de desenvolvimento diferentes das de produção
- [ ] Chaves de API têm permissões mínimas necessárias

### Rotação de Credenciais
- [ ] Trocar chaves periodicamente
- [ ] Revogar chaves antigas
- [ ] Atualizar em todos os ambientes
- [ ] Testar após mudança

## 🔍 Auditoria

### Logs de Segurança
O sistema registra tentativas de acesso sem credenciais:
```typescript
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Variáveis de ambiente não configuradas');
}
```

### Monitoramento
- Monitor de uso da API Gemini
- Logs de autenticação do Supabase
- Alertas de uso anômalo

## 📋 Troubleshooting

### Erro: "Variáveis de ambiente não configuradas"
```bash
# Verifique se o arquivo existe
ls -la .env.local

# Verifique o conteúdo (sem mostrar valores)
grep -o '^[^=]*' .env.local
```

### Erro: "Failed to fetch" em produção
- Verifique se as variáveis estão configuradas no provedor
- Confirme que os valores estão corretos
- Teste em ambiente de preview primeiro

### Chaves não funcionam
- Verifique se as chaves não expiraram
- Confirme permissões no Supabase/Google Cloud
- Teste com curl/Postman primeiro

## 🔗 Links Úteis

- [Supabase API Keys](https://supabase.com/dashboard/project/_/settings/api)
- [Google AI Studio](https://makersuite.google.com/app/apikey)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)

---

**Importante**: Nunca compartilhe suas credenciais reais em issues, pull requests ou documentação pública!
