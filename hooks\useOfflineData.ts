import { useState, useEffect, useCallback } from 'react';
import { WordEntry } from '../types';
import {
  LocalWordEntry,
  getAllWordsLocally,
  addWordLocally,
  removeWordLocally,
  updateWordLocally,
  clearLocalData,
  hasPendingSync,
  getSyncStats
} from '../services/localStorageService';
import {
  syncWithSupabase,
  getSyncStatus,
  setupConnectivityListener,
  SyncResult,
  SyncStatus
} from '../services/syncService';

export interface UseOfflineDataReturn {
  // Dados
  words: LocalWordEntry[];
  isLoading: boolean;
  error: string | null;

  // Ações de palavras
  addWord: (text: string) => Promise<boolean>;
  removeWord: (id: string) => Promise<boolean>;
  updateWord: (id: string, updates: Partial<WordEntry>) => Promise<boolean>;

  // Sincronização
  syncStatus: SyncStatus;
  syncData: () => Promise<SyncResult>;
  isSyncing: boolean;
  lastSyncResult: SyncResult | null;

  // Utilitários
  clearAllData: () => void;
  refreshData: () => void;
  loadInitialData: () => Promise<void>;
  hasPendingChanges: boolean;
  syncStats: ReturnType<typeof getSyncStats>;
}

export const useOfflineData = (userId: string | null): UseOfflineDataReturn => {
  const [words, setWords] = useState<LocalWordEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(() => getSyncStatus());
  const [hasPendingChanges, setHasPendingChanges] = useState(false);
  const [syncStats, setSyncStats] = useState(() => getSyncStats());

  // Carregar dados locais
  const loadLocalData = useCallback(() => {
    if (!userId) {
      setWords([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const localWords = getAllWordsLocally(userId);
      setWords(localWords.sort((a, b) => new Date(b.added_date).getTime() - new Date(a.added_date).getTime()));
      setError(null);
    } catch (err) {
      setError('Erro ao carregar dados locais');
      console.error('Erro ao carregar dados locais:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Carregar dados iniciais (local + servidor se necessário)
  const loadInitialData = useCallback(async () => {
    if (!userId) {
      setWords([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Primeiro, carregar dados locais
      const localWords = getAllWordsLocally(userId);

      // Se não há dados locais e está online, tentar carregar do servidor
      if (localWords.length === 0 && navigator.onLine) {
        try {
          console.log('Nenhum dado local encontrado. Carregando do Supabase...');
          const result = await syncWithSupabase();

          if (result.success) {
            // Recarregar dados locais após sincronização
            const updatedLocalWords = getAllWordsLocally(userId);
            setWords(updatedLocalWords.sort((a, b) => new Date(b.added_date).getTime() - new Date(a.added_date).getTime()));
            console.log(`Dados carregados do Supabase: ${updatedLocalWords.length} palavras`);
          } else {
            console.warn('Falha ao carregar dados do Supabase:', result.errors);
            setWords(localWords);
          }
        } catch (err) {
          console.error('Erro ao carregar dados do servidor:', err);
          setWords(localWords);
        }
      } else {
        // Usar dados locais existentes
        setWords(localWords.sort((a, b) => new Date(b.added_date).getTime() - new Date(a.added_date).getTime()));
        if (localWords.length > 0) {
          console.log(`Dados locais carregados: ${localWords.length} palavras`);
        }
      }
    } catch (err) {
      setError('Erro ao carregar dados');
      console.error('Erro ao carregar dados:', err);
      setWords([]);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Atualizar status de sincronização
  const updateSyncStatus = useCallback(() => {
    const status = getSyncStatus();
    setSyncStatus(status);
    setHasPendingChanges(hasPendingSync());
    setSyncStats(getSyncStats());
  }, []);

  // Adicionar palavra
  const addWord = useCallback(async (text: string): Promise<boolean> => {
    if (!userId) {
      setError('Usuário não autenticado');
      return false;
    }

    try {
      // Verificar duplicatas
      const normalizedText = text.toLowerCase();
      if (words.some(word => word.text.toLowerCase() === normalizedText)) {
        setError(`A palavra "${text}" já foi adicionada.`);
        setTimeout(() => setError(null), 3000);
        return false;
      }

      const newWord = addWordLocally(text, userId);
      setWords(prevWords => [newWord, ...prevWords]);
      updateSyncStatus();
      setError(null);
      return true;
    } catch (err) {
      setError('Erro ao adicionar palavra');
      console.error('Erro ao adicionar palavra:', err);
      return false;
    }
  }, [userId, words, updateSyncStatus]);

  // Remover palavra
  const removeWord = useCallback(async (id: string): Promise<boolean> => {
    try {
      const success = removeWordLocally(id);
      if (success) {
        setWords(prevWords => prevWords.filter(word => word.id !== id && word.localId !== id));
        updateSyncStatus();
        setError(null);
        return true;
      }
      return false;
    } catch (err) {
      setError('Erro ao remover palavra');
      console.error('Erro ao remover palavra:', err);
      return false;
    }
  }, [updateSyncStatus]);

  // Atualizar palavra
  const updateWord = useCallback(async (id: string, updates: Partial<WordEntry>): Promise<boolean> => {
    try {
      const success = updateWordLocally(id, updates);
      if (success) {
        setWords(prevWords =>
          prevWords.map(word =>
            (word.id === id || word.localId === id)
              ? { ...word, ...updates, lastModified: new Date().toISOString() }
              : word
          )
        );
        updateSyncStatus();
        setError(null);
        return true;
      }
      return false;
    } catch (err) {
      setError('Erro ao atualizar palavra');
      console.error('Erro ao atualizar palavra:', err);
      return false;
    }
  }, [updateSyncStatus]);

  // Sincronizar dados
  const syncData = useCallback(async (): Promise<SyncResult> => {
    if (!userId) {
      const result: SyncResult = {
        success: false,
        syncedCount: 0,
        errorCount: 1,
        errors: ['Usuário não autenticado'],
        conflictsCount: 0
      };
      return result;
    }

    setIsSyncing(true);
    setError(null);

    try {
      const result = await syncWithSupabase();
      setLastSyncResult(result);

      if (result.success) {
        // Recarregar dados após sincronização bem-sucedida
        loadLocalData();
        updateSyncStatus();
      } else if (result.errors.length > 0) {
        setError(result.errors[0]);
      }

      return result;
    } catch (err) {
      const errorResult: SyncResult = {
        success: false,
        syncedCount: 0,
        errorCount: 1,
        errors: ['Erro de sincronização'],
        conflictsCount: 0
      };
      setLastSyncResult(errorResult);
      setError('Erro de sincronização');
      console.error('Erro de sincronização:', err);
      return errorResult;
    } finally {
      setIsSyncing(false);
    }
  }, [userId, loadLocalData, updateSyncStatus]);

  // Limpar todos os dados
  const clearAllData = useCallback(() => {
    clearLocalData();
    setWords([]);
    updateSyncStatus();
    setError(null);
  }, [updateSyncStatus]);

  // Atualizar dados
  const refreshData = useCallback(() => {
    loadLocalData();
    updateSyncStatus();
  }, [loadLocalData, updateSyncStatus]);

  // Efeito para carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  // Efeito para atualizar status de sincronização
  useEffect(() => {
    updateSyncStatus();
  }, [words, updateSyncStatus]);

  // Efeito para listener de conectividade
  useEffect(() => {
    const cleanup = setupConnectivityListener((isOnline) => {
      setSyncStatus(prev => ({ ...prev, isOnline }));
    });

    return cleanup;
  }, []);

  // Efeito para limpar dados quando usuário muda
  useEffect(() => {
    if (!userId) {
      setWords([]);
      setError(null);
      setIsLoading(false);
    }
  }, [userId]);

  return {
    // Dados
    words,
    isLoading,
    error,

    // Ações de palavras
    addWord,
    removeWord,
    updateWord,

    // Sincronização
    syncStatus: { ...syncStatus, isSyncing },
    syncData,
    isSyncing,
    lastSyncResult,

    // Utilitários
    clearAllData,
    refreshData,
    loadInitialData, // Expor função para carregamento inicial
    hasPendingChanges,
    syncStats
  };
};
