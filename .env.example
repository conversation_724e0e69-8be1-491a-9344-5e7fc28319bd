# Exemplo de arquivo de configuração de ambiente
# Copie este arquivo para .env.local e preencha com suas credenciais reais

# Google Gemini API Key
# Obtenha em: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=sua_chave_da_api_gemini_aqui

# Supabase Configuration
# Obtenha em: https://supabase.com/dashboard/project/[seu-projeto]/settings/api
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase_aqui

# Instruções:
# 1. Copie este arquivo: cp .env.example .env.local
# 2. Substitua os valores pelos suas credenciais reais
# 3. Nunca commite o arquivo .env.local no git (já está no .gitignore)
